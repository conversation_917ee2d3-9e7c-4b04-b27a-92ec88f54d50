/**
 * Performance optimization utilities for improving Core Web Vitals
 * Focuses on reducing CLS, improving LCP, and optimizing FID
 */

// Debounce function for performance optimization
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// Throttle function for scroll and resize events
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Optimize animations for better performance
export function optimizeAnimation(element: HTMLElement, animation: Keyframe[] | PropertyIndexedKeyframes, options?: KeyframeAnimationOptions) {
  // Check if user prefers reduced motion
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    return null;
  }

  // Use Web Animations API for better performance
  if ('animate' in element) {
    return element.animate(animation, {
      duration: 300,
      easing: 'ease-out',
      fill: 'forwards',
      ...options
    });
  }

  return null;
}

// Preload critical resources
export function preloadResource(href: string, as: string, type?: string, crossorigin?: boolean) {
  if (typeof document === 'undefined') return;

  // Check if already preloaded
  const existing = document.querySelector(`link[rel="preload"][href="${href}"]`);
  if (existing) return;

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  
  if (type) link.type = type;
  if (crossorigin) link.crossOrigin = 'anonymous';
  
  document.head.appendChild(link);
}

// Optimize images for better LCP
export function optimizeImageLoading() {
  if (typeof document === 'undefined') return;

  // Add loading="lazy" to images below the fold
  const images = document.querySelectorAll('img:not([loading])');
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        if (!img.loading) {
          img.loading = 'lazy';
        }
        imageObserver.unobserve(img);
      }
    });
  });

  images.forEach((img) => {
    imageObserver.observe(img);
  });
}

// Reduce Cumulative Layout Shift (CLS)
export function preventLayoutShift() {
  if (typeof document === 'undefined') return;

  // Add dimensions to images without them
  const images = document.querySelectorAll('img:not([width]):not([height])');
  images.forEach((img) => {
    const htmlImg = img as HTMLImageElement;
    if (htmlImg.naturalWidth && htmlImg.naturalHeight) {
      htmlImg.width = htmlImg.naturalWidth;
      htmlImg.height = htmlImg.naturalHeight;
    }
  });

  // Reserve space for dynamic content
  const dynamicElements = document.querySelectorAll('[data-dynamic-height]');
  dynamicElements.forEach((element) => {
    const htmlElement = element as HTMLElement;
    const minHeight = htmlElement.dataset.dynamicHeight;
    if (minHeight) {
      htmlElement.style.minHeight = minHeight;
    }
  });
}

// Optimize font loading to reduce CLS
export function optimizeFontLoading() {
  if (typeof document === 'undefined') return;

  // Add font-display: swap to all font-face declarations
  const style = document.createElement('style');
  style.textContent = `
    @font-face {
      font-display: swap;
    }
  `;
  document.head.appendChild(style);
}

// Measure and report Core Web Vitals
export function measureWebVitals() {
  if (typeof window === 'undefined' || !window.PerformanceObserver) return;

  // Measure LCP
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
    }).observe({ type: 'largest-contentful-paint', buffered: true });
  } catch (e) {
    // LCP not supported
  }

  // Measure FID
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry) => {
        const perfEntry = entry as PerformanceEventTiming;
        console.log('FID:', perfEntry.processingStart - perfEntry.startTime);
      });
    }).observe({ type: 'first-input', buffered: true });
  } catch (e) {
    // FID not supported
  }

  // Measure CLS
  try {
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry) => {
        const layoutShiftEntry = entry as LayoutShift;
        if (!layoutShiftEntry.hadRecentInput) {
          clsValue += layoutShiftEntry.value;
        }
      });
      console.log('CLS:', clsValue);
    }).observe({ type: 'layout-shift', buffered: true });
  } catch (e) {
    // CLS not supported
  }
}

// Optimize critical rendering path
export function optimizeCriticalRenderingPath() {
  if (typeof document === 'undefined') return;

  // Inline critical CSS
  const criticalCSS = `
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
    .loading { opacity: 0; }
    .loaded { opacity: 1; transition: opacity 0.3s ease; }
  `;

  const style = document.createElement('style');
  style.textContent = criticalCSS;
  document.head.insertBefore(style, document.head.firstChild);

  // Mark page as loaded
  window.addEventListener('load', () => {
    document.body.classList.add('loaded');
  });
}

// Advanced Core Web Vitals monitoring
export function initializeCoreWebVitalsMonitoring() {
  if (typeof window === 'undefined') return;

  // Monitor Largest Contentful Paint (LCP)
  const observeLCP = () => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];

      console.log('LCP:', lastEntry.startTime);

      // Send to analytics if available
      if (window.gtag) {
        window.gtag('event', 'web_vitals', {
          name: 'LCP',
          value: Math.round(lastEntry.startTime),
          event_category: 'Web Vitals'
        });
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  };

  // Monitor First Input Delay (FID)
  const observeFID = () => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: PerformanceEventTiming) => {
        console.log('FID:', entry.processingStart - entry.startTime);

        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            name: 'FID',
            value: Math.round(entry.processingStart - entry.startTime),
            event_category: 'Web Vitals'
          });
        }
      });
    });

    observer.observe({ entryTypes: ['first-input'] });
  };

  // Monitor Cumulative Layout Shift (CLS)
  const observeCLS = () => {
    let clsValue = 0;
    const clsEntries: PerformanceEntry[] = [];

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();

      entries.forEach((entry: LayoutShift) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          clsEntries.push(entry);
        }
      });

      console.log('CLS:', clsValue);

      if (window.gtag) {
        window.gtag('event', 'web_vitals', {
          name: 'CLS',
          value: Math.round(clsValue * 1000),
          event_category: 'Web Vitals'
        });
      }
    });

    observer.observe({ entryTypes: ['layout-shift'] });
  };

  // Initialize all observers
  observeLCP();
  observeFID();
  observeCLS();
}

// Performance budget monitoring
export function monitorPerformanceBudget() {
  if (typeof window === 'undefined') return;

  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

    const metrics = {
      TTFB: navigation.responseStart - navigation.requestStart,
      FCP: 0, // Will be set by observer
      LCP: 0, // Will be set by observer
      DOMContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      LoadComplete: navigation.loadEventEnd - navigation.loadEventStart
    };

    // Check against performance budgets
    const budgets = {
      TTFB: 600, // 600ms
      FCP: 1800, // 1.8s
      LCP: 2500, // 2.5s
      DOMContentLoaded: 1000, // 1s
      LoadComplete: 3000 // 3s
    };

    Object.entries(metrics).forEach(([metric, value]) => {
      if (value > budgets[metric as keyof typeof budgets]) {
        console.warn(`Performance budget exceeded for ${metric}: ${value}ms (budget: ${budgets[metric as keyof typeof budgets]}ms)`);
      }
    });

    console.log('Performance Metrics:', metrics);
  });
}

// Optimize third-party scripts
export function optimizeThirdPartyScripts() {
  if (typeof document === 'undefined') return;

  // Defer non-critical scripts
  const scripts = document.querySelectorAll('script[src]:not([async]):not([defer])');
  scripts.forEach((script) => {
    const htmlScript = script as HTMLScriptElement;
    if (!htmlScript.src.includes('critical')) {
      htmlScript.defer = true;
    }
  });
}

// Initialize all performance optimizations
export function initializePerformanceOptimizations() {
  if (typeof window === 'undefined') return;

  // Run optimizations when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      optimizeImageLoading();
      preventLayoutShift();
      optimizeFontLoading();
      optimizeCriticalRenderingPath();
      optimizeThirdPartyScripts();
    });
  } else {
    optimizeImageLoading();
    preventLayoutShift();
    optimizeFontLoading();
    optimizeCriticalRenderingPath();
    optimizeThirdPartyScripts();
  }

  // Initialize advanced monitoring
  initializeCoreWebVitalsMonitoring();
  monitorPerformanceBudget();

  // Measure performance in production
  if (process.env.NODE_ENV === 'production') {
    measureWebVitals();
  }

  console.log('Performance optimizations initialized');
}

export default {
  debounce,
  throttle,
  optimizeAnimation,
  preloadResource,
  optimizeImageLoading,
  preventLayoutShift,
  optimizeFontLoading,
  measureWebVitals,
  optimizeCriticalRenderingPath,
  optimizeThirdPartyScripts,
  initializePerformanceOptimizations
};
